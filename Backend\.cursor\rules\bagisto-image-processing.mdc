# Bagisto 图片处理模式和最佳实践

本规则记录了Bagisto中图片处理的标准模式，包括上传、压缩、存储和显示的最佳实践。

## 🖼️ 核心图片处理组件

### 标准图片上传组件
**组件**: [x-admin::media.images](mdc:Backend/packages/Webkul/Admin/src/Resources/views/components/media/images.blade.php)

```blade
<x-admin::media.images
    name="images[files]"
    allow-multiple="true"
    show-placeholders="true"
    :uploaded-images="$existingImages"
/>
```

### 组件属性说明
- `name`: 表单字段名称
- `allow-multiple`: 是否允许多文件上传
- `show-placeholders`: 是否显示占位符
- `uploaded-images`: 已上传图片数组

## 📁 图片存储策略

### 标准存储路径结构
```
storage/app/
├── product/{product_id}/          # 产品相关图片
├── category/{category_id}/        # 分类图片
├── customer/{customer_id}/        # 客户头像等
├── cms/                          # CMS内容图片
└── temp/                         # 临时上传文件
```

### 文件命名规范
```php
// 标准命名模式
$filename = Str::random(40) . '.webp';

// 路径构造
$path = 'product/' . $product->id . '/' . $filename;
```

## 🔧 图片压缩和格式转换

### WebP压缩标准实现
**参考**: [ProductMediaRepository.php](mdc:Backend/packages/Webkul/Product/src/Repositories/ProductMediaRepository.php)

```php
use Intervention\Image\ImageManager;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

// 标准压缩流程
$manager = new ImageManager;
$image = $manager->make($uploadedFile)->encode('webp');
$path = 'product/' . $product->id . '/' . Str::random(40) . '.webp';
Storage::put($path, $image);
```

### 压缩配置选项
```php
// 高质量压缩（默认）
$image->encode('webp');

// 自定义质量压缩
$image->encode('webp', 85);

// 尺寸调整 + 压缩
$image->resize(1200, 1200, function($constraint) {
    $constraint->aspectRatio();
    $constraint->upsize();
})->encode('webp');
```

## 💾 数据库存储模式

### 单图片存储
```php
// 直接存储路径字符串到text_value
'image_path' => 'product/123/abc123...xyz.webp'
```

### 多图片存储
```php
// JSON数组存储到json_value
'image_paths' => [
    "product/123/abc123...xyz.webp",
    "product/123/def456...uvw.webp"
]
```

### 产品主图片存储
**表**: `product_images`
```php
Schema::table('product_images', function($table) {
    $table->string('type');      // main, additional
    $table->string('path');      // 文件路径
    $table->integer('position'); // 排序位置
});
```

## 🔄 图片处理工作流

### 上传处理标准流程
```php
// 1. 验证文件
$this->validate($request, [
    'images.files.*' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048'
]);

// 2. 处理现有图片保持
$existingImages = $this->getExistingImages($product);

// 3. 处理新上传图片
$newImages = [];
foreach ($request->file('images.files', []) as $file) {
    if ($file instanceof UploadedFile) {
        $newImages[] = $this->processAndStore($file, $product);
    }
}

// 4. 合并和保存
$allImages = array_merge($existingImages, $newImages);
$this->saveImageData($product, $allImages);
```

### 删除处理流程
```php
// 检查删除标记
if ($request->input('images.delete', [])) {
    foreach ($request->input('images.delete') as $imageId) {
        $this->deleteImage($imageId);
    }
}
```

## 🎨 前端显示模式

### 图片预览组件模式
```blade
@php
    $formattedImages = collect($imagePaths)->map(function($path) {
        return [
            'id' => $path,
            'url' => Storage::url($path)
        ];
    })->toArray();
@endphp

<x-admin::media.images
    :uploaded-images="$formattedImages"
/>
```

### 图片数据格式转换
```php
// 从路径字符串转换为组件所需格式
$uploadedImages = collect($paths)->map(function($path) {
    return [
        'id' => $path,        // 用于表单字段key
        'url' => Storage::url($path),  // 显示URL
        'name' => basename($path),     // 文件名（可选）
        'size' => Storage::size($path) // 文件大小（可选）
    ];
})->toArray();
```

## 🔒 安全和验证

### 文件类型验证
```php
// 标准验证规则
'images.files.*' => [
    'image',
    'mimes:jpeg,png,jpg,gif,svg,webp',
    'max:2048', // 2MB
    'dimensions:min_width=100,min_height=100'
]
```

### 文件名安全处理
```php
// 避免使用原始文件名，使用随机生成
$safeFilename = Str::random(40) . '.webp';

// 路径安全检查
$safePath = str_replace(['..', '//', '\\'], '', $path);
```

### 存储权限控制
```php
// 确保存储目录权限
if (!Storage::exists($directory)) {
    Storage::makeDirectory($directory);
}

// 检查文件存在性
if (Storage::exists($path)) {
    Storage::delete($path);
}
```

## ⚡ 性能优化策略

### 1. 延迟加载
```php
// 使用延迟加载避免N+1查询
$products = Product::with('images')->get();
```

### 2. 缓存优化
```php
// URL生成缓存
Cache::remember("image_url_{$path}", 3600, function() use ($path) {
    return Storage::url($path);
});
```

### 3. CDN集成
```php
// 配置CDN前缀
$cdnUrl = config('app.cdn_url');
$imageUrl = $cdnUrl . '/' . $path;
```

### 4. 响应式图片
```php
// 生成多尺寸版本
$thumbnails = [
    'thumb' => $image->resize(150, 150),
    'medium' => $image->resize(400, 400),
    'large' => $image->resize(800, 800),
];
```

## 🛠️ 错误处理和恢复

### 上传失败处理
```php
try {
    $path = $this->processImage($file);
} catch (Exception $e) {
    Log::error('Image upload failed: ' . $e->getMessage());
    return back()->withError('图片上传失败，请重试');
}
```

### 文件损坏检测
```php
// 验证图片完整性
try {
    $image = $manager->make($file);
    $image->encode('webp'); // 尝试编码验证
} catch (Exception $e) {
    throw new InvalidImageException('图片文件损坏');
}
```

### 存储空间检查
```php
// 检查可用空间
$availableSpace = disk_free_space(storage_path());
$fileSize = $file->getSize();

if ($availableSpace < $fileSize * 2) { // 预留空间
    throw new InsufficientStorageException('存储空间不足');
}
```

## 📱 移动端优化

### 图片尺寸适配
```php
// 移动端图片优化
if ($this->isMobile()) {
    $image->resize(800, 800, function($constraint) {
        $constraint->aspectRatio();
    });
} else {
    $image->resize(1200, 1200, function($constraint) {
        $constraint->aspectRatio();
    });
}
```

### 渐进式加载
```blade
<!-- 使用懒加载 -->
<img 
    src="data:image/placeholder" 
    data-src="{{ Storage::url($path) }}"
    class="lazy-load"
    loading="lazy"
/>
```

## 🔄 迁移和备份

### 图片迁移脚本
```php
// 批量格式转换
public function convertToWebP()
{
    $images = Storage::files('product');
    
    foreach ($images as $image) {
        if (!str_ends_with($image, '.webp')) {
            $this->convertImageToWebP($image);
        }
    }
}
```

### 备份策略
```php
// 图片备份到云存储
public function backupImages()
{
    $localImages = Storage::disk('local')->allFiles('product');
    
    foreach ($localImages as $image) {
        Storage::disk('s3')->put($image, Storage::get($image));
    }
}
```

## 📊 监控和分析

### 存储使用统计
```php
// 统计图片存储使用情况
$totalSize = collect(Storage::allFiles('product'))
    ->sum(fn($file) => Storage::size($file));
    
$imageCount = Product::has('images')->count();
```

### 性能监控
```php
// 图片处理性能记录
$startTime = microtime(true);
$this->processImage($file);
$processingTime = microtime(true) - $startTime;

Log::info('Image processing time: ' . $processingTime . 's');
```

---
*此规则提供了Bagisto图片处理的完整指南，涵盖了从上传到显示的整个生命周期管理。*
description:
globs:
alwaysApply: false
---
