# Bagisto 自定义属性类型开发模式

基于multiimage属性实现的经验，本规则总结了在Bagisto中开发自定义属性类型的通用模式和最佳实践。

## 🏗️ 属性类型开发标准流程

### 1. 枚举定义阶段
**位置**: [AttributeTypeEnum.php](mdc:Backend/packages/Webkul/Attribute/src/Enums/AttributeTypeEnum.php)

```php
// 使用大写常量名，小写值
case CUSTOM_TYPE = 'custom_type';
```

### 2. 数据映射配置
**位置**: [Attribute.php](mdc:Backend/packages/Webkul/Attribute/src/Models/Attribute.php)
**位置**: [ProductAttributeValue.php](mdc:Backend/packages/Webkul/Product/src/Models/ProductAttributeValue.php)

```php
protected static $attributeTypeFields = [
    'custom_type' => 'text_value',    // 简单文本
    'complex_type' => 'json_value',   // 复杂JSON数据
    'number_type' => 'float_value',   // 数字类型
    'date_type' => 'datetime_value',  // 日期时间
    'bool_type' => 'boolean_value',   // 布尔值
];
```

### 3. 数据处理仓储层
**位置**: [ProductAttributeValueRepository.php](mdc:Backend/packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php)

```php
// 在saveValues方法中添加自定义处理逻辑
if ($attribute->type === 'custom_type') {
    // 自定义数据处理逻辑
    $data[$attribute->code] = $this->processCustomData($data[$attribute->code]);
}
```

### 4. 模型数据获取逻辑
**位置**: [Product.php](mdc:Backend/packages/Webkul/Product/src/Models/Product.php)

```php
// 在getCustomAttributeValue方法中添加
if ($attribute->type === 'custom_type') {
    return $this->formatCustomValue($value);
}
```

## 🎨 前端UI开发模式

### 1. 控制器集成
**位置**: [controls.blade.php](mdc:Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/controls.blade.php)

```php
@case('custom_type')
    <!-- 简单属性可直接在这里实现 -->
    <x-admin::form.control-group.control
        type="text"
        :name="$attribute->code"
        :value="$product[$attribute->code]"
    />
    @break

@case('complex_type')
    {{-- 复杂属性重定向到专门组件 --}}
    {{-- complex_type is handled in dedicated section --}}
    @break
```

### 2. 专用组件开发
对于复杂属性类型，创建专门的Blade文件：

```blade
<!-- custom-attributes.blade.php -->
@php
    $customAttributes = $product->attribute_family->custom_attributes()
        ->where('type', 'complex_type')
        ->get();
@endphp

@if($customAttributes->isNotEmpty())
    @foreach($customAttributes as $attribute)
        <div class="box-shadow relative rounded bg-white p-4 dark:bg-gray-900">
            <!-- 自定义UI实现 -->
        </div>
    @endforeach
@endif
```

### 3. 页面集成策略

根据属性特性选择集成位置：
- **简单输入类型**: 保留在controls.blade.php中
- **媒体相关类型**: 集成到images/videos区域
- **关系型数据**: 集成到links区域
- **配置型数据**: 单独创建区块

## 🌐 多语言支持模式

### 翻译文件结构
```php
// en/app.php 和对应语言文件
'catalog' => [
    'attributes' => [
        'create' => [
            'type' => [
                'custom_type' => 'Custom Type Name',
            ]
        ],
        'edit' => [
            'custom_type' => 'Custom Type Name',
        ],
        'index' => [
            'datagrid' => [
                'custom_type' => 'Custom Type Name',
            ]
        ]
    ]
]
```

### 数据网格配置
**位置**: [AttributeDataGrid.php](mdc:Backend/packages/Webkul/Admin/src/DataGrids/Catalog/AttributeDataGrid.php)

```php
[
    'label' => trans('admin::app.catalog.attributes.index.datagrid.custom_type'),
    'value' => 'custom_type',
],
```

## 💾 数据存储策略选择

### 存储字段映射规则
- **text_value**: 简单文本、单文件路径、逗号分隔列表
- **json_value**: 复杂对象、数组、多文件路径
- **float_value**: 数字、价格、百分比
- **datetime_value**: 日期时间戳
- **boolean_value**: 开关状态

### JSON存储格式规范
```php
// 数组类型
["item1", "item2", "item3"]

// 对象类型
{
    "config": "value",
    "items": ["item1", "item2"],
    "metadata": {"key": "value"}
}

// 文件路径数组
["path/to/file1.ext", "path/to/file2.ext"]
```

## 🔧 验证和安全考虑

### 控制器验证规则
**位置**: [ThemeController.php](mdc:Backend/packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php) 或相关控制器

```php
'type' => 'required|in:text,image,custom_type,multiimage',
```

### 数据验证模式
```php
// 在属性配置中定义验证规则
'validation_rules' => 'required|custom_validation_rule',

// 在saveValues中应用验证
$this->validate($request, [
    $attribute->code => $attribute->validation_rules,
]);
```

## ⚡ 性能优化建议

### 1. 延迟加载策略
- 复杂属性使用独立组件避免影响主表单
- 大文件处理使用异步上传
- 预览图使用缩略图

### 2. 缓存策略
```php
// 属性选项缓存
Cache::remember("attribute_options_{$attribute->id}", 3600, function() {
    return $this->attribute->options;
});
```

### 3. 数据库优化
- 合适的索引策略
- JSON字段查询优化
- 分页和筛选优化

## 🧪 测试和验证模式

### 功能测试检查点
```php
// 测试用例结构
class CustomAttributeTest extends TestCase
{
    /** @test */
    public function it_can_create_custom_attribute() {}
    
    /** @test */
    public function it_can_save_custom_attribute_value() {}
    
    /** @test */
    public function it_can_retrieve_custom_attribute_value() {}
}
```

### UI测试验证
- [ ] 属性创建页面正确显示
- [ ] 属性编辑功能正常
- [ ] 产品编辑页面集成正确
- [ ] 数据保存和读取一致
- [ ] 多语言支持完整
- [ ] 响应式布局适配

## 📁 文件组织最佳实践

### 目录结构建议
```
Backend/packages/Webkul/
├── Attribute/
│   ├── src/
│   │   ├── Enums/AttributeTypeEnum.php          # 类型定义
│   │   └── Models/Attribute.php                 # 映射配置
├── Product/
│   ├── src/
│   │   ├── Models/
│   │   │   ├── Product.php                      # 数据获取
│   │   │   └── ProductAttributeValue.php        # 值存储
│   │   └── Repositories/
│   │       └── ProductAttributeValueRepository.php # 数据处理
└── Admin/
    └── src/Resources/views/catalog/products/edit/
        ├── controls.blade.php                   # 简单控件
        ├── custom-attributes.blade.php          # 复杂组件
        └── edit.blade.php                       # 页面集成
```

## 🔄 版本升级兼容性

### 向后兼容原则
1. 新属性类型不影响现有类型
2. 数据库结构变更使用迁移
3. 配置文件保持向后兼容
4. API接口版本管理

### 迁移脚本模式
```php
// 添加新属性类型的迁移示例
public function up()
{
    // 更新现有数据兼容新格式
    DB::table('product_attribute_values')
        ->where('attribute_type', 'old_type')
        ->update(['attribute_type' => 'new_type']);
}
```

---
*此规则总结了基于实际开发经验的Bagisto属性类型开发最佳实践，可用于指导后续自定义属性开发。*
description:
globs:
alwaysApply: false
---
