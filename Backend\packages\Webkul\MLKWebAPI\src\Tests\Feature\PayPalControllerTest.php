<?php

namespace Webkul\MLKWebAPI\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Webkul\Customer\Models\Customer;
use Webkul\Checkout\Facades\Cart;
use Laravel\Sanctum\Sanctum;

class PayPalControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->customer = Customer::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_verified' => 1,
            'status' => 1,
        ]);
    }

    /**
     * 测试 PayPal Smart Button 创建订单 - 未认证用户
     */
    public function test_create_smart_button_order_unauthenticated()
    {
        $response = $this->postJson('/api/paypal/smart-button/create-order');

        $response->assertStatus(401);
    }

    /**
     * 测试 PayPal Smart Button 创建订单 - 空购物车
     */
    public function test_create_smart_button_order_empty_cart()
    {
        Sanctum::actingAs($this->customer);

        $response = $this->postJson('/api/paypal/smart-button/create-order');

        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                 ]);
    }

    /**
     * 测试 PayPal Smart Button 捕获订单 - 无效数据
     */
    public function test_capture_smart_button_order_invalid_data()
    {
        Sanctum::actingAs($this->customer);

        $response = $this->postJson('/api/paypal/smart-button/capture-order', [
            'orderData' => []
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['orderData.orderID']);
    }

    /**
     * 测试 PayPal Standard 获取重定向信息 - 未认证用户
     */
    public function test_get_standard_redirect_info_unauthenticated()
    {
        $response = $this->getJson('/api/paypal/standard/redirect-info');

        $response->assertStatus(401);
    }

    /**
     * 测试 PayPal Standard 成功回调 - 未认证用户
     */
    public function test_standard_success_unauthenticated()
    {
        $response = $this->getJson('/api/paypal/standard/success');

        $response->assertStatus(401);
    }

    /**
     * 测试 PayPal Standard 取消回调 - 未认证用户
     */
    public function test_standard_cancel_unauthenticated()
    {
        $response = $this->getJson('/api/paypal/standard/cancel');

        $response->assertStatus(401);
    }

    /**
     * 测试获取支付状态 - 无效订单ID
     */
    public function test_get_payment_status_invalid_order_id()
    {
        Sanctum::actingAs($this->customer);

        $response = $this->getJson('/api/paypal/payment-status?order_id=999999');

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['order_id']);
    }

    /**
     * 测试 PayPal IPN 处理
     */
    public function test_handle_ipn()
    {
        $ipnData = [
            'payment_status' => 'Completed',
            'txn_id' => 'TEST123456789',
            'receiver_email' => '<EMAIL>',
            'mc_gross' => '100.00',
            'mc_currency' => 'USD',
        ];

        $response = $this->postJson('/api/paypal/ipn', $ipnData);

        $response->assertStatus(200)
                 ->assertJson(['status' => 'success']);
    }

    /**
     * 测试路由是否正确注册
     */
    public function test_paypal_routes_are_registered()
    {
        $routes = [
            'mlk.api.paypal.smart_button.create_order',
            'mlk.api.paypal.smart_button.capture_order',
            'mlk.api.paypal.standard.redirect_info',
            'mlk.api.paypal.standard.success',
            'mlk.api.paypal.standard.cancel',
            'mlk.api.paypal.payment_status',
            'mlk.api.paypal.ipn',
        ];

        foreach ($routes as $routeName) {
            $this->assertTrue(
                \Route::has($routeName),
                "Route {$routeName} is not registered"
            );
        }
    }

    /**
     * 测试 API 响应格式
     */
    public function test_api_response_format()
    {
        Sanctum::actingAs($this->customer);

        $response = $this->getJson('/api/paypal/standard/cancel');

        $response->assertJsonStructure([
            'success',
            'message',
            'data',
            'locale',
        ]);

        $this->assertFalse($response->json('success'));
        $this->assertIsString($response->json('message'));
        $this->assertIsArray($response->json('data'));
        $this->assertIsString($response->json('locale'));
    }
}
