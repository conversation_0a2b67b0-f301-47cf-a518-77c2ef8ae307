# Bagisto MultiImage 属性实现指南

本规则记录了在Bagisto中实现multiimage属性类型的完整实现过程，包括存储、压缩、UI展示等功能。

## 📋 核心文件修改清单

### 1. 枚举类型定义
**文件**: [Backend/packages/Webkul/Attribute/src/Enums/AttributeTypeEnum.php](mdc:Backend/packages/Webkul/Attribute/src/Enums/AttributeTypeEnum.php)

```php
case MULTIIMAGE = 'multiimage';
```

### 2. 属性模型配置
**文件**: [Backend/packages/Webkul/Attribute/src/Models/Attribute.php](mdc:Backend/packages/Webkul/Attribute/src/Models/Attribute.php)

```php
protected static $attributeTypeFields = [
    // ... 其他类型
    'multiimage' => 'json_value',  // 使用json_value存储JSON数组
];
```

### 3. 产品属性值模型
**文件**: [Backend/packages/Webkul/Product/src/Models/ProductAttributeValue.php](mdc:Backend/packages/Webkul/Product/src/Models/ProductAttributeValue.php)

```php
public static $attributeTypeFields = [
    // ... 其他类型
    'multiimage' => 'json_value',
];
```

### 4. 数据保存逻辑
**文件**: [Backend/packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php](mdc:Backend/packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php)

关键功能：
- 图片上传和WebP压缩
- 现有图片保持
- JSON格式存储路径数组

### 5. 产品模型数据获取
**文件**: [Backend/packages/Webkul/Product/src/Models/Product.php](mdc:Backend/packages/Webkul/Product/src/Models/Product.php)

处理multiimage类型的JSON解码逻辑。

## 🎨 前端UI实现

### 1. 控制器修改
**文件**: [Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/controls.blade.php](mdc:Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/controls.blade.php)

multiimage类型被重定向到专门的显示区域。

### 2. 专用多图显示组件
**文件**: [Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/multiimages.blade.php](mdc:Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/multiimages.blade.php)

- 自动检测multiimage属性
- 独立显示区块
- 使用标准media.images组件

### 3. 产品编辑页面集成
**文件**: [Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit.blade.php](mdc:Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit.blade.php)

在产品主图片下方显示multiimage属性。

## 🌐 多语言支持

### 英文翻译
**文件**: [Backend/packages/Webkul/Admin/src/Resources/lang/en/app.php](mdc:Backend/packages/Webkul/Admin/src/Resources/lang/en/app.php)

```php
// 在 catalog.attributes.create.type 中添加
'multiimage' => 'Multi Image',

// 在 catalog.attributes.edit 中添加
'multiimage' => 'Multi Image',

// 在 catalog.attributes.index.datagrid 中添加
'multiimage' => 'Multi Image',
```

### 中文翻译
**文件**: [Backend/packages/Webkul/Admin/src/Resources/lang/zh_CN/app.php](mdc:Backend/packages/Webkul/Admin/src/Resources/lang/zh_CN/app.php)

对应的中文翻译。

## 🔧 数据网格配置

**文件**: [Backend/packages/Webkul/Admin/src/DataGrids/Catalog/AttributeDataGrid.php](mdc:Backend/packages/Webkul/Admin/src/DataGrids/Catalog/AttributeDataGrid.php)

在筛选选项中添加multiimage类型。

## 💾 图片处理技术细节

### WebP压缩实现
```php
use Intervention\Image\ImageManager;

$manager = new ImageManager;
$image = $manager->make($file)->encode('webp');
$path = 'product/' . $product->id . '/' . Str::random(40) . '.webp';
Storage::put($path, $image);
```

### 数据存储格式
- **存储位置**: `product_attribute_values.json_value`
- **格式**: JSON数组 `["path/to/image1.webp", "path/to/image2.webp"]`
- **文件命名**: 40位随机字符串 + `.webp`

## 🚀 实现优势

1. **WebP压缩**: 相比JPG减少25-35%文件大小
2. **模块化设计**: multiimage属性独立显示，不影响其他属性
3. **UI一致性**: 使用与产品主图片相同的组件和样式
4. **响应式布局**: 支持单列和双列布局
5. **事件钩子**: 支持插件扩展

## ⚠️ 重要注意事项

1. **存储字段**: 必须使用`json_value`而非`text_value`
2. **图片压缩**: 统一使用WebP格式，与产品主图片保持一致
3. **文件保持**: 正确处理现有图片的保存逻辑
4. **UI位置**: multiimage显示在产品图片模块下方，位置更合理
5. **多语言**: 确保所有相关翻译都已添加

## 🧪 测试检查点

- [ ] 属性创建页面显示multiimage选项
- [ ] 产品编辑页面正确显示multiimage区块
- [ ] 图片上传和压缩功能正常
- [ ] 现有图片保存逻辑正确
- [ ] 多语言显示正常
- [ ] 响应式布局工作正常

## 📂 相关组件参考

- 标准图片组件: [x-admin::media.images](mdc:Backend/packages/Webkul/Admin/src/Resources/views/components/media/images.blade.php)
- 产品主图片: [images.blade.php](mdc:Backend/packages/Webkul/Admin/src/Resources/views/catalog/products/edit/images.blade.php)
- 图片处理仓储: [ProductMediaRepository.php](mdc:Backend/packages/Webkul/Product/src/Repositories/ProductMediaRepository.php)

---
*此规则基于Bagisto multiimage属性的完整实现经验，提供了从后端逻辑到前端UI的完整解决方案。*
description:
globs:
alwaysApply: false
---
